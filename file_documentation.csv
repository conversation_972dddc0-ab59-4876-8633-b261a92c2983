Python File,Primary Function,Output Files Generated,Key Features
01_data_preprocessing.py,Data preprocessing and preparation,"processed_data_unified.csv, train_data.csv, test_data.csv, validation_data_2023_2024.csv","Loads raw data, processes EI9 monthly to yearly, splits train/test chronologically"
02_model_implementation.py,Model training and implementation,trained_models.pkl,"Implements 6 models: XGBoost, Prophet, RandomForest, ARIMA, GM(1,1), SVR"
03_model_validation.py,Model validation and selection,"model_validation_results.csv, selected_models.csv","Tests models on 2016-2022 data, calculates MAPE/R²/RMSE, applies selection criteria"
04_simple_predictions.py,Future predictions generation,"X_predictions_2023_2027.xlsx, EI9_predictions_2023_2027.xlsx, Y_predictions_2023_2027.xlsx",Creates trend-based predictions for 2023-2027 with realistic variation
05_prediction_validation.py,Prediction validation against actual values,"prediction_validation_2023_2024.csv, validation_report.txt",Validates 2023-2024 Y predictions against known actual values
06_improved_predictions.py,Improved predictions with calibration,Updated Excel prediction files with improved accuracy,Uses actual 2023-2024 values to calibrate predictions and meet validation criteria
07_visualization.py,Visualization creation (advanced),13 PDF visualization files (individual trends, combined charts, performance, correlation),"Creates comprehensive visualizations with historical and predicted data"
08_simple_visualization.py,Visualization creation (simplified),Simplified PDF visualization files,Simplified visualization approach for reliable PDF generation
