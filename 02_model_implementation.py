"""
Model Implementation and Training for Time Series Forecasting
This script implements 6 different models: XGBoost, Prophet, RandomForest, ARIMA, GM(1,1), SVR
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Machine Learning Models
from xgboost import XGBRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error

# Time Series Models
from prophet import Prophet
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import adfuller

# Utilities
import pickle
import os
from datetime import datetime
import matplotlib.pyplot as plt

class GM11:
    """GM(1,1) Grey Model implementation"""
    def __init__(self):
        self.a = None
        self.b = None
        self.fitted_values = None

    def fit(self, X):
        X = np.array(X).flatten()
        n = len(X)

        # Accumulate the data
        X1 = np.cumsum(X)

        # Create the data matrix
        B = np.zeros((n-1, 2))
        Y = X[1:].reshape(-1, 1)

        for i in range(n-1):
            B[i, 0] = -(X1[i] + X1[i+1]) / 2
            B[i, 1] = 1

        # Calculate parameters using least squares
        try:
            params = np.linalg.inv(B.T @ B) @ B.T @ Y
            self.a = params[0, 0]
            self.b = params[1, 0]

            # Calculate fitted values
            self.fitted_values = []
            self.fitted_values.append(X[0])

            for i in range(1, n):
                pred = (X[0] - self.b/self.a) * np.exp(-self.a * i) + self.b/self.a
                self.fitted_values.append(pred - sum(self.fitted_values))

        except:
            # Fallback to simple linear trend if matrix inversion fails
            self.a = 0.1
            self.b = np.mean(X)
            self.fitted_values = X.tolist()

        return self

    def predict(self, steps):
        if self.a is None or self.b is None:
            return [self.fitted_values[-1]] * steps

        predictions = []
        n = len(self.fitted_values)

        for i in range(steps):
            pred = (self.fitted_values[0] - self.b/self.a) * np.exp(-self.a * (n + i)) + self.b/self.a
            if i == 0:
                pred_diff = pred - sum(self.fitted_values)
            else:
                pred_diff = pred - (sum(self.fitted_values) + sum(predictions))
            predictions.append(max(pred_diff, 0))  # Ensure non-negative predictions

        return predictions

class TimeSeriesModels:
    """Class to implement and manage multiple time series models"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.model_performance = {}
        
    def prepare_features(self, data, target_col, lag_features=3):
        """Prepare features for ML models with lag features"""
        df = data.copy()
        df = df.sort_values('年份')
        
        # Create lag features
        for i in range(1, lag_features + 1):
            df[f'{target_col}_lag_{i}'] = df[target_col].shift(i)
            
        # Create trend feature
        df['trend'] = range(len(df))
        
        # Create year-based features
        df['year_norm'] = (df['年份'] - df['年份'].min()) / (df['年份'].max() - df['年份'].min())
        
        # Remove rows with NaN values (due to lag features)
        df = df.dropna()
        
        return df
    
    def implement_gm11(self, data):
        """Implement GM(1,1) Grey Model"""
        return GM11()
    
    def train_xgboost(self, train_data, target_col, region):
        """Train XGBoost model"""
        print(f"Training XGBoost for {target_col} in {region}...")
        
        region_data = train_data[train_data['区域'] == region].copy()
        df_features = self.prepare_features(region_data, target_col)
        
        # Prepare features and target
        feature_cols = [col for col in df_features.columns if col not in ['区域', '年份', target_col]]
        X = df_features[feature_cols]
        y = df_features[target_col]
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Train model
        model = XGBRegressor(n_estimators=100, max_depth=6, random_state=42)
        model.fit(X_scaled, y)
        
        # Store model and scaler
        model_key = f"xgboost_{target_col}_{region}"
        self.models[model_key] = model
        self.scalers[model_key] = scaler
        
        return model, scaler
    
    def train_prophet(self, train_data, target_col, region):
        """Train Prophet model"""
        print(f"Training Prophet for {target_col} in {region}...")
        
        region_data = train_data[train_data['区域'] == region].copy()
        region_data = region_data.sort_values('年份')
        
        # Prepare data for Prophet
        prophet_data = pd.DataFrame({
            'ds': pd.to_datetime(region_data['年份'], format='%Y'),
            'y': region_data[target_col]
        })
        
        # Train model
        model = Prophet(yearly_seasonality=True, daily_seasonality=False, weekly_seasonality=False)
        model.fit(prophet_data)
        
        # Store model
        model_key = f"prophet_{target_col}_{region}"
        self.models[model_key] = model
        
        return model
    
    def train_random_forest(self, train_data, target_col, region):
        """Train Random Forest model"""
        print(f"Training Random Forest for {target_col} in {region}...")
        
        region_data = train_data[train_data['区域'] == region].copy()
        df_features = self.prepare_features(region_data, target_col)
        
        # Prepare features and target
        feature_cols = [col for col in df_features.columns if col not in ['区域', '年份', target_col]]
        X = df_features[feature_cols]
        y = df_features[target_col]
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Train model
        model = RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42)
        model.fit(X_scaled, y)
        
        # Store model and scaler
        model_key = f"random_forest_{target_col}_{region}"
        self.models[model_key] = model
        self.scalers[model_key] = scaler
        
        return model, scaler
    
    def train_arima(self, train_data, target_col, region):
        """Train ARIMA model"""
        print(f"Training ARIMA for {target_col} in {region}...")
        
        region_data = train_data[train_data['区域'] == region].copy()
        region_data = region_data.sort_values('年份')
        
        # Prepare time series
        ts_data = region_data[target_col].values
        
        # Check stationarity and difference if needed
        try:
            adf_result = adfuller(ts_data)
            if adf_result[1] > 0.05:  # Non-stationary
                ts_data = np.diff(ts_data)
                d = 1
            else:
                d = 0
        except:
            d = 1
            ts_data = np.diff(ts_data)
        
        # Train ARIMA model
        try:
            model = ARIMA(ts_data, order=(2, 0, 1))  # Using d=0 since we pre-differenced
            fitted_model = model.fit()
            
            # Store model
            model_key = f"arima_{target_col}_{region}"
            self.models[model_key] = {'model': fitted_model, 'differenced': d, 'original_data': region_data[target_col].values}
            
            return fitted_model
        except:
            # Fallback to simple ARIMA
            model = ARIMA(region_data[target_col].values, order=(1, 1, 1))
            fitted_model = model.fit()
            
            model_key = f"arima_{target_col}_{region}"
            self.models[model_key] = {'model': fitted_model, 'differenced': 1, 'original_data': region_data[target_col].values}
            
            return fitted_model
    
    def train_gm11(self, train_data, target_col, region):
        """Train GM(1,1) model"""
        print(f"Training GM(1,1) for {target_col} in {region}...")
        
        region_data = train_data[train_data['区域'] == region].copy()
        region_data = region_data.sort_values('年份')
        
        # Train GM(1,1) model
        model = self.implement_gm11(region_data[target_col].values)
        model.fit(region_data[target_col].values)
        
        # Store model
        model_key = f"gm11_{target_col}_{region}"
        self.models[model_key] = model
        
        return model
    
    def train_svr(self, train_data, target_col, region):
        """Train SVR model"""
        print(f"Training SVR for {target_col} in {region}...")
        
        region_data = train_data[train_data['区域'] == region].copy()
        df_features = self.prepare_features(region_data, target_col)
        
        # Prepare features and target
        feature_cols = [col for col in df_features.columns if col not in ['区域', '年份', target_col]]
        X = df_features[feature_cols]
        y = df_features[target_col]
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Train model
        model = SVR(kernel='rbf', C=100, gamma='scale')
        model.fit(X_scaled, y)
        
        # Store model and scaler
        model_key = f"svr_{target_col}_{region}"
        self.models[model_key] = model
        self.scalers[model_key] = scaler
        
        return model, scaler

def main():
    """Main training function"""
    print("="*60)
    print("TIME SERIES FORECASTING - MODEL TRAINING")
    print("="*60)

    try:
        # Load processed data
        print("Loading training data...")
        train_data = pd.read_csv('train_data.csv')
        print(f"Training data loaded: {train_data.shape}")

        # Initialize model manager
        ts_models = TimeSeriesModels()

        # Define targets and regions
        targets = ['Y', 'X', 'EI9']
        regions = ['J区', 'H区', 'L区']

        print(f"Training models for {len(targets)} targets across {len(regions)} regions...")
        print(f"Targets: {targets}")
        print(f"Regions: {regions}")

        # Train all models for all combinations
        total_combinations = len(targets) * len(regions)
        current_combination = 0

        for target in targets:
            for region in regions:
                current_combination += 1
                print(f"\n[{current_combination}/{total_combinations}] Training models for {target} in {region}")

                # Train each model type with error handling
                model_types = ['XGBoost', 'Prophet', 'Random Forest', 'ARIMA', 'GM(1,1)', 'SVR']

                for model_type in model_types:
                    try:
                        if model_type == 'XGBoost':
                            ts_models.train_xgboost(train_data, target, region)
                        elif model_type == 'Prophet':
                            ts_models.train_prophet(train_data, target, region)
                        elif model_type == 'Random Forest':
                            ts_models.train_random_forest(train_data, target, region)
                        elif model_type == 'ARIMA':
                            ts_models.train_arima(train_data, target, region)
                        elif model_type == 'GM(1,1)':
                            ts_models.train_gm11(train_data, target, region)
                        elif model_type == 'SVR':
                            ts_models.train_svr(train_data, target, region)

                        print(f"  ✓ {model_type} trained successfully")

                    except Exception as e:
                        print(f"  ✗ Error training {model_type}: {str(e)}")

        # Save trained models
        print("\nSaving trained models...")
        try:
            with open('trained_models.pkl', 'wb') as f:
                pickle.dump(ts_models, f)
            print("Models saved successfully to trained_models.pkl")
        except Exception as e:
            print(f"Error saving models with pickle: {str(e)}")
            print("Saving model information to text file instead...")

            # Save model information as text
            with open('trained_models_info.txt', 'w') as f:
                f.write("Trained Models Information\n")
                f.write("="*50 + "\n")
                f.write(f"Total models trained: {len(ts_models.models)}\n\n")

                for model_key in ts_models.models.keys():
                    f.write(f"Model: {model_key}\n")

            print("Model information saved to trained_models_info.txt")

        print(f"\nTotal models trained: {len(ts_models.models)}")
        print("Model training completed successfully!")

    except Exception as e:
        print(f"Critical error in main function: {str(e)}")
        import traceback
        traceback.print_exc()

    print("\n" + "="*60)
    print("MODEL TRAINING COMPLETED!")
    print("="*60)

if __name__ == "__main__":
    main()
