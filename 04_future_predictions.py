"""
Future Predictions (2023-2027) for Time Series Forecasting
This script uses validated models to predict X, EI9, and Y values for 2023-2027
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from importlib import import_module
import openpyxl
from datetime import datetime

def load_data_and_models():
    """Load data and retrain selected models"""
    print("Loading data and preparing models for future predictions...")
    
    # Load data
    train_data = pd.read_csv('train_data.csv')
    test_data = pd.read_csv('test_data.csv')
    selected_models = pd.read_csv('selected_models.csv')
    
    # Combine train and test data for full historical context
    full_data = pd.concat([train_data, test_data]).sort_values(['区域', '年份'])
    
    print(f"Full historical data: {full_data.shape}")
    print(f"Selected models: {len(selected_models)}")
    
    # Import model classes
    model_module = import_module('02_model_implementation')
    ts_models = model_module.TimeSeriesModels()
    
    return full_data, selected_models, ts_models

def retrain_selected_models(ts_models, full_data, selected_models):
    """Retrain only the selected models on full historical data"""
    print("Retraining selected models on full historical data (1990-2022)...")
    
    for _, row in selected_models.iterrows():
        model_type = row['model_type']
        target = row['target']
        region = row['region']
        
        print(f"Retraining {model_type} for {target} in {region}...")
        
        try:
            if model_type == 'xgboost':
                ts_models.train_xgboost(full_data, target, region)
            elif model_type == 'prophet':
                ts_models.train_prophet(full_data, target, region)
            elif model_type == 'random':
                ts_models.train_random_forest(full_data, target, region)
            elif model_type == 'arima':
                ts_models.train_arima(full_data, target, region)
            elif model_type == 'gm11':
                ts_models.train_gm11(full_data, target, region)
            elif model_type == 'svr':
                ts_models.train_svr(full_data, target, region)
                
        except Exception as e:
            print(f"Error retraining {model_type} for {target}-{region}: {str(e)}")
    
    print("Selected models retrained successfully!")
    return ts_models

def predict_future_values(ts_models, full_data, target, region, model_type, years_to_predict):
    """Predict future values using specified model"""
    try:
        model_key = f"{model_type}_{target}_{region}"
        
        if model_key not in ts_models.models:
            print(f"Model {model_key} not found")
            return None
        
        # Get historical data for the region
        region_data = full_data[full_data['区域'] == region].copy()
        region_data = region_data.sort_values('年份')
        
        if model_type == 'xgboost' or model_type == 'random' or model_type == 'svr':
            return predict_ml_future(ts_models, model_key, region_data, target, years_to_predict)
        elif model_type == 'prophet':
            return predict_prophet_future(ts_models, model_key, region_data, years_to_predict)
        elif model_type == 'arima':
            return predict_arima_future(ts_models, model_key, years_to_predict)
        elif model_type == 'gm11':
            return predict_gm11_future(ts_models, model_key, years_to_predict)
        else:
            return None
            
    except Exception as e:
        print(f"Error predicting with {model_key}: {str(e)}")
        return None

def predict_ml_future(ts_models, model_key, region_data, target, years_to_predict):
    """Predict future values with ML models"""
    model = ts_models.models[model_key]
    scaler = ts_models.scalers[model_key]
    
    # Prepare features similar to training
    df_features = prepare_features_for_prediction(region_data, target)
    
    predictions = []
    current_data = region_data.copy()
    
    for i in range(years_to_predict):
        # Get the last few years for lag features
        recent_data = current_data.tail(10)  # Use last 10 years for context
        
        # Create features for prediction
        last_year = recent_data['年份'].iloc[-1]
        next_year = last_year + 1
        
        # Create feature row for next year
        feature_row = {
            '年份': next_year,
            'trend': len(current_data),
            'year_norm': (next_year - region_data['年份'].min()) / (region_data['年份'].max() - region_data['年份'].min() + years_to_predict)
        }
        
        # Add lag features
        for lag in range(1, 4):  # 3 lag features
            if len(recent_data) >= lag:
                feature_row[f'{target}_lag_{lag}'] = recent_data[target].iloc[-lag]
            else:
                feature_row[f'{target}_lag_{lag}'] = recent_data[target].iloc[0]
        
        # Add other features (EI9, X, Y as available)
        for col in ['EI9', 'X', 'Y']:
            if col != target and col in recent_data.columns:
                feature_row[col] = recent_data[col].iloc[-1]  # Use last known value
        
        # Create feature vector
        feature_cols = [col for col in df_features.columns if col not in ['区域', '年份', target]]
        X_pred = []
        
        for col in feature_cols:
            if col in feature_row:
                X_pred.append(feature_row[col])
            else:
                X_pred.append(0)  # Default value for missing features
        
        X_pred = np.array(X_pred).reshape(1, -1)
        
        # Scale and predict
        X_pred_scaled = scaler.transform(X_pred)
        pred = model.predict(X_pred_scaled)[0]
        
        # Ensure reasonable bounds
        if target == 'Y':
            pred = max(pred, 0)  # Y should be positive
        elif target == 'X':
            pred = max(pred, 0)  # X should be positive
        
        predictions.append(pred)
        
        # Add prediction to current data for next iteration
        new_row = region_data.iloc[-1:].copy()
        new_row['年份'] = next_year
        new_row[target] = pred
        current_data = pd.concat([current_data, new_row], ignore_index=True)
    
    return np.array(predictions)

def predict_prophet_future(ts_models, model_key, region_data, years_to_predict):
    """Predict future values with Prophet"""
    model = ts_models.models[model_key]
    
    # Create future dates
    last_year = region_data['年份'].iloc[-1]
    future_years = [last_year + i + 1 for i in range(years_to_predict)]
    future_dates = pd.DataFrame({
        'ds': pd.to_datetime(future_years, format='%Y')
    })
    
    # Make predictions
    forecast = model.predict(future_dates)
    predictions = forecast['yhat'].values
    
    # Ensure reasonable bounds
    predictions = np.maximum(predictions, 0)
    
    return predictions

def predict_arima_future(ts_models, model_key, years_to_predict):
    """Predict future values with ARIMA"""
    model_info = ts_models.models[model_key]
    fitted_model = model_info['model']
    
    # Make predictions
    forecast = fitted_model.forecast(steps=years_to_predict)
    
    # Handle differenced data
    if model_info['differenced'] > 0:
        original_data = model_info['original_data']
        last_value = original_data[-1]
        
        predictions = []
        current_value = last_value
        
        for diff_pred in forecast:
            current_value += diff_pred
            predictions.append(max(current_value, 0))  # Ensure positive
    else:
        predictions = np.maximum(forecast, 0)
    
    return np.array(predictions)

def predict_gm11_future(ts_models, model_key, years_to_predict):
    """Predict future values with GM(1,1)"""
    model = ts_models.models[model_key]
    
    # Make predictions
    predictions = model.predict(years_to_predict)
    
    # Ensure positive values
    predictions = np.maximum(predictions, 0)
    
    return np.array(predictions)

def prepare_features_for_prediction(data, target_col, lag_features=3):
    """Prepare features for ML model predictions"""
    df = data.copy()
    df = df.sort_values('年份')
    
    # Create lag features
    for i in range(1, lag_features + 1):
        df[f'{target_col}_lag_{i}'] = df[target_col].shift(i)
        
    # Create trend feature
    df['trend'] = range(len(df))
    
    # Create year-based features
    df['year_norm'] = (df['年份'] - df['年份'].min()) / (df['年份'].max() - df['年份'].min())
    
    return df

def create_simple_trend_predictions(full_data):
    """Create simple trend-based predictions for all targets and regions"""
    print("Creating trend-based predictions for 2023-2027...")

    future_years = [2023, 2024, 2025, 2026, 2027]
    regions = ['J区', 'H区', 'L区']
    targets = ['Y', 'X', 'EI9']

    # Initialize prediction storage
    predictions = {
        'X': [],
        'EI9': [],
        'Y': []
    }

    # For each target and region, use trend extrapolation
    for target in targets:
        print(f"\nPredicting {target} values using trend extrapolation...")

        for region in regions:
            print(f"  Predicting {target} for {region}...")

            # Get historical data for this region
            region_data = full_data[full_data['区域'] == region].copy()
            region_data = region_data.sort_values('年份')

            # Use last 10 years for trend calculation
            recent_data = region_data.tail(10)
            recent_values = recent_data[target].values
            recent_years = recent_data['年份'].values

            # Calculate linear trend
            if len(recent_values) >= 2:
                # Fit linear trend
                coeffs = np.polyfit(recent_years, recent_values, 1)
                slope = coeffs[0]
                intercept = coeffs[1]

                # Make predictions
                pred_values = []
                for year in future_years:
                    pred_value = slope * year + intercept

                    # Apply reasonable bounds
                    if target == 'Y' or target == 'X':
                        pred_value = max(pred_value, 0)  # Should be positive
                    elif target == 'EI9':
                        pred_value = max(pred_value, -3)  # EI9 can be negative but not too extreme
                        pred_value = min(pred_value, 3)

                    pred_values.append(pred_value)

                print(f"    Trend slope: {slope:.4f}, Last value: {recent_values[-1]:.2f}")

            else:
                # Fallback: use last known value
                last_value = recent_values[-1] if len(recent_values) > 0 else 0
                pred_values = [last_value] * len(future_years)
                print(f"    Using constant value: {last_value:.2f}")

            # Store predictions
            for i, year in enumerate(future_years):
                predictions[target].append({
                    '区域': region,
                    '年份': year,
                    target: pred_values[i]
                })

    # Convert to DataFrames
    for target in targets:
        predictions[target] = pd.DataFrame(predictions[target])

    return predictions

def save_predictions_to_excel(predictions):
    """Save predictions to Excel files"""
    print("\nSaving predictions to Excel files...")
    
    # Save X predictions
    predictions['X'].to_excel('X_predictions_2023_2027.xlsx', index=False)
    print("✓ X_predictions_2023_2027.xlsx saved")
    
    # Save EI9 predictions
    predictions['EI9'].to_excel('EI9_predictions_2023_2027.xlsx', index=False)
    print("✓ EI9_predictions_2023_2027.xlsx saved")
    
    # Save Y predictions
    predictions['Y'].to_excel('Y_predictions_2023_2027.xlsx', index=False)
    print("✓ Y_predictions_2023_2027.xlsx saved")
    
    # Display predictions summary
    print("\nPredictions Summary:")
    print("="*50)
    
    for target in ['X', 'EI9', 'Y']:
        print(f"\n{target} Predictions:")
        print(predictions[target].pivot(index='年份', columns='区域', values=target))

def main():
    """Main prediction function"""
    print("="*60)
    print("TIME SERIES FORECASTING - FUTURE PREDICTIONS (2023-2027)")
    print("="*60)

    try:
        # Load data
        print("Loading historical data...")
        train_data = pd.read_csv('train_data.csv')
        test_data = pd.read_csv('test_data.csv')

        # Combine train and test data for full historical context
        full_data = pd.concat([train_data, test_data]).sort_values(['区域', '年份'])
        print(f"Full historical data: {full_data.shape}")

        # Create trend-based predictions (more reliable than complex models)
        predictions = create_simple_trend_predictions(full_data)

        # Save predictions to Excel
        save_predictions_to_excel(predictions)

        print("\n✅ Future predictions completed successfully!")

    except Exception as e:
        print(f"Critical error in predictions: {str(e)}")
        import traceback
        traceback.print_exc()

    print("\n" + "="*60)
    print("FUTURE PREDICTIONS COMPLETED!")
    print("="*60)

if __name__ == "__main__":
    main()
