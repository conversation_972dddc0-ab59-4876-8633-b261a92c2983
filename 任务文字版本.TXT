预测模型选择：XGboost  Prophet  RandomForest  ARIMA  GM(1,1)  SVR最终至少选取3个模型（遇到效果不满足的模型剔除），模型是并列的，满足不了3个及时告知我。
1 首先用1990-2022“原始数据.xlsx”中80%的数据（前26年: 90-15年）用作训练集，后20%数据（后7年: 16-22年）用作测试集，因为是时间序列数据，需要严格按时间划分。用MAPE < 10% (最大不能超15%)、R²>0.6（R²绝不可小于0.3）、RMSE来验证模型的可靠性，不符合要求的模型剔除。（MAPE, R², RMSE值要在python终端显示）。
2 然后用1中符合条件的模型，分别预测23-27年的X, EI9的数值并另存excel。将预测23-27年的X, EI9输入符合条件的预测模型（选取最合适的模型进行预测），预测出23-27年Y值并另存excel。随后，再用已知J、H和L区域真实23-24年的Y值（见下表），检测23-24这两年预测的Y值的相对误差绝对值需要控制在10%内，此处存在模型选择（3个区域，检测6个数值，若遇问题请及时沟通），满足条件即可！注意预测各区Y值不能是近似等差数列，变化幅度肯定不一样，MAPE和R²值绝不可能都一样！！！
	区域	年份	真实Y	预测Y	
1	J区	   2023	   682.06		
2	J区	   2024	   676.38		
3	H区	   2023	   2440.00		
4	H区	   2024	   2471.60		
5	L区	   2023	   412.34		
6	L区	   2024	   398.40	

相对误差绝对值= |(预测值-真实值)）÷真实值| < 10%
最多2个相对误差的绝对值可以小于15% 

3 绘图部分：
绘制1990-2027年三个区域 J、H、L 中EI9的数据趋势图， X的趋势图， Y的趋势图，每个区域单独一张图另存pdf且能plt.show()，此处包含9张图。历史数据90-22年用圆点实线连接，23-27年用小方块实线连接（不显示数据和涨幅百分比），历史数据和预测数据间的断点（22年的圆点和23年的方块）用虚线连接，连接线使用3种不同颜色。
J、H、L三区域Y值历史和预测数据额外一起再绘制在一张图上另存pdf。
模型性能对比图MAPE和 R²（都满足1中的条件），各一张图。
J、H、L三区域中Y、X和EI9的相关性热力图，放1个pdf即可。

4 Python项目文件需标记清楚，不使用重复的文件名。注明生成X、EI9 和Y预测的excel数据来源哪一个python代码；生成pdf图片来源于哪一个代码；模型性能分析来源哪一个代码。模型使用情况说明的CSV文件，为什么最终选择这个最优模型。 

Check: 一共有3个另存的excel表、13张pdf图和1个CSV说明文件。
