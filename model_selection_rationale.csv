Model,Model Type,Strengths,Weaknesses,Performance on Validation,Selection Decision,Final Usage
XGBoost,Gradient Boosting,"Handles non-linear relationships, feature importance, robust to outliers","Requires feature engineering, can overfit, black box model","Good for Y predictions in L区 (MAPE: 2.57%, R²: 0.60)",SELECTED - Best performing model for Y in L区,Primary model for Y predictions in L区
Prophet,Time Series Decomposition,"Handles seasonality and trends, uncertainty quantification, interpretable","Requires trend/seasonality assumptions, sensitive to outliers","Moderate performance across indicators (MAPE: 3-20%)",PARTIALLY SELECTED - Good for some indicators,Used for trend analysis and uncertainty estimation
Random Forest,Ensemble Learning,"Reduces overfitting, handles mixed data types, feature importance","Can overfit with many trees, less interpretable than single trees",Models not found in final validation (training issues),NOT SELECTED - Training/validation issues,Excluded due to technical issues
ARIMA,Autoregressive,"Classical time series method, handles autocorrelation, statistical foundation","Assumes stationarity, limited non-linear capability, parameter selection","Good for X predictions (MAPE: 2-8%), poor for EI9",SELECTED - Good performance for X predictions,Used for X predictions across regions
GM(1,1),Grey System Theory,"Works with limited data, exponential smoothing, simple implementation","Limited theoretical foundation, assumes exponential growth, simple assumptions",Poor performance overall (high MAPE values),NOT SELECTED - Poor overall performance,Excluded from final predictions
SVR,Support Vector Machine,"Non-linear mapping, kernel methods, robust to high dimensions","Parameter tuning required, computationally expensive, kernel selection","Mixed results, good for Y in L区 (MAPE: 3.61%)",SELECTED - Acceptable performance for Y predictions,Used as secondary model for Y predictions
